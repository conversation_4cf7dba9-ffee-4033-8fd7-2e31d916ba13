using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Mercury.Api.Extensions;

internal sealed class AuthSecuritySchemeTransformer : IOpenApiDocumentTransformer
{
    private const string BearerAuthDescription = @"Ingrese el token Bearer (JWT) para la autorización. 
                            Su punto final de token de actualización
                            (por ejemplo, /auth/refresh-token) debe llamarse por separado.";
    private const string BearerFormat = "JWT";
    private const string BearerScheme = "bearer";
    
    
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        document.Components ??= new OpenApiComponents();

        document.Components.SecuritySchemes ??= new Dictionary<string, OpenApiSecurityScheme>();
        
        var bearerScheme = new OpenApiSecurityScheme
        {
            Type = SecuritySchemeType.Http,
            Scheme = BearerScheme,
            BearerFormat = BearerFormat,
            Description = BearerAuthDescription
        };
        
        document.Components.SecuritySchemes[ScalarDefinitions.BearerAuthSchemeName] = bearerScheme;
        
        document.SecurityRequirements.Add(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = ScalarDefinitions.BearerAuthSchemeName
                    }
                },
                (List<string>) []
            }
        });

        return Task.CompletedTask;
    }
}